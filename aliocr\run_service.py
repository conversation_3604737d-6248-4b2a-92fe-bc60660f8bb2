#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import logging
from logging.handlers import RotatingFileHandler
import http_speech_service

# 创建日志目录
if not os.path.exists('logs'):
    os.makedirs('logs')

# 配置日志
handler = RotatingFileHandler('logs/speech_service.log', maxBytes=10000000, backupCount=5)
handler.setFormatter(logging.Formatter(
    '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
))
handler.setLevel(logging.INFO)

# 添加到应用
http_speech_service.app.logger.addHandler(handler)
http_speech_service.app.logger.setLevel(logging.INFO)
http_speech_service.app.logger.info('Speech Recognition Service startup')

# 启动应用
if __name__ == '__main__':
    http_speech_service.app.run(host='0.0.0.0', port=5000, debug=False, threaded=True) 