<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use QL\QueryList;
use GuzzleHttp\Client;
use HeadlessChromium\BrowserFactory;

class FetchQuote extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fetch-quote {min_id} {max_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '二手行情价格采集';

    protected $baseUrl = 'https://www.timez.cn';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 把watches表内为 2 的记录更新为 0
        // DB::table('watches')
        //     ->where('is_quote', 2)
        //     ->update([
        //         'is_quote' => 0
        //     ]);

        // dd('done');

        $min_id = $this->argument('min_id');
        $max_id = $this->argument('max_id');

        $this->recursiveScrapeWebsite($min_id, $max_id);
    }

    /**
     * 递归抓取网页内容
     */
    public function recursiveScrapeWebsite($min_id, $max_id) 
    {
        try {
            $this->info('开始抓取二手行情价格');

            // 初始化浏览器（仅一次）
            $browserFactory = new BrowserFactory();
            $browser = $browserFactory->createBrowser([
                'headless' => true,
                'connectionDelay' => 0.8,
                'windowSize' => [1920, 1080],
            ]);

            while (true) {

                $watch = DB::table('watches')
                    ->where('is_quote', 0)
                    ->where('id', '>=', $min_id)
                    ->where('id', '<', $max_id)
                    ->first();

                if (!$watch) {
                    $this->info('数据抓取完成');
                    break;
                }

                $url = "https://www.timez.cn/search/{$watch->reference_number}";
                $htmlContent = $this->scrapeWebsite($url, $browser);
                $href = QueryList::html($htmlContent)->find('.watch-list-wrap a:first')->attr('href');
                QueryList::html($htmlContent)->destruct();
                $htmlContent = null;
                
                if (empty($href)) {
                    DB::table('watches')
                        ->where('id', $watch->id)
                        ->update([
                            'is_quote' => 2
                        ]);

                    $this->info("产品不存在，型号: {$watch->reference_number}");
                }
                else
                {
                    $link = "{$this->baseUrl}{$href}"; 
                    $htmlContent = $this->scrapeWebsite($link, $browser);
                    $used_market_price = QueryList::html($htmlContent)->find('.detail-top-right h3:last')->html();
                    $elements = QueryList::html($htmlContent)->find('.detail-top-right span');
                    $change_percent = $elements->eq($elements->count() - 2)->html();
                    QueryList::html($htmlContent)->destruct();
                    $htmlContent = null;
                    
                    if(!empty($used_market_price) || !empty($change_percent))
                    {
                        DB::table('watches_quote')->insert([
                            'watches_id' => $watch->id,
                            'used_market_price' => trim($used_market_price, '¥'), // 过滤掉¥
                            'change_percent' => trim($change_percent, '%') // 过滤掉%
                        ]);

                        DB::table('watches')
                            ->where('id', $watch->id)
                            ->update([
                                'is_quote' => 1
                            ]);

                        $this->info("二手行情价格完成采集，型号: {$watch->reference_number}");
                    }
                    else
                    {   
                        DB::table('watches')
                            ->where('id', $watch->id)
                            ->update([
                                'is_quote' => 2
                            ]);

                        $this->info("二手行情价格不存在，型号: {$watch->reference_number}");
                    }
                }

                // 释放内存
                unset($watch, $href);
                gc_collect_cycles();
            }

        } 
        catch (\Throwable $e) 
        {
            $this->info("程序异常: " . $e->getMessage());
        } 
        finally {
            if (isset($browser)) {
                $browser->close();
            }
        }
    }

    /**
     * 使用无头浏览器抓取网页内容
     */
    public function scrapeWebsite($url, $browser) 
    {    
        try {
            // 创建新页面
            $page = $browser->createPage();
            
            // 导航到目标网页并等待加载完成
            $page->navigate($url)->waitForNavigation();
            
            // 等待页面完全加载（包括动态内容）
            sleep(3);
            
            // 获取页面完整HTML源代码（包括JavaScript渲染后的内容）
            // $html = $page->evaluate('document.documentElement.outerHTML')->getReturnValue();
            
            // 返回抓取的源代码
            return $page->getHtml();
        } 
        catch (\Throwable $e) 
        {
            return '<html></html>';
        } 
        finally {
            if (isset($page)) {
                $page->close();
            }
        }
    }
}
