<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use QL\QueryList;
use GuzzleHttp\Client;
use HeadlessChromium\BrowserFactory;

class ProcessWatchesQueue extends Command
{
    /**
     * 控制台命令运行方法和命令
     * 
     * [nohup php artisan app:process-watches-queue > /dev/null 2>&1 &]
     * [ps aux | grep process-watches-queue]
     *
     * @var string
     */
    protected $signature = 'app:process-watches-queue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '定时检查watches_fech_queue表中status为0的记录并处理';

    /**
     * 时研家网站
     */
    protected $baseUrl = 'https://www.timez.cn';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 启用垃圾回收
        gc_enable();
        
        $this->info('开始监控watches_fech_queue表...');
        
        while (true) {
            try {
                $this->checkAndProcessQueue();
                $this->info('本批次处理完成，等待15秒继续执行...');
            } catch (\Throwable $e) {
                $this->info('程序异常：' . $e->getMessage());
            }

            // 强制回收 PHP 垃圾
            gc_collect_cycles();

            // 等待15秒
            sleep(15);
        }
    }

    /**
     * 检查并处理队列中的记录
     */
    protected function checkAndProcessQueue()
    {
        // 查询status为0的记录
        $queueItems = DB::table('watches_fech_queue')
            ->where('status', 0)
            ->orderBy('id', 'asc')
            ->limit(50)
            ->get();
        
        if ($queueItems->isEmpty()) {
            return;
        }

        try {

            // 初始化浏览器
            $browser = (new BrowserFactory(env('BROWSER_BIN')))->createBrowser([
                'headless'       => true,
                'noSandbox'      => true,
                'connectionDelay'=> 0.8,
                'windowSize'     => [1920, 1080],
            ]);

            // 循环处理每条记录
            foreach ($queueItems as $item) {
                
                try {

                    $watch = DB::table('watches')
                        ->where('id', $item->watches_id)
                        ->first();
                    
                    if (!$watch) {
                        DB::table('watches_fech_queue')
                            ->where('id', $item->id)
                            ->update([
                                'status' => 2,
                                'error_message' => '此手表ID不存在'
                            ]);
                        continue;
                    }

                    $url = "{$this->baseUrl}/search/{$watch->reference_number}";
                    $htmlContent = $this->scrapeWebsite($browser, $url, '.watch-list-wrap a', 10000);

                    $ql = QueryList::html($htmlContent);
                    $href = $ql->find('.watch-list-wrap a:first')->attr('href');
                    // dd($href);
                    
                    if (empty($href)) {
                        DB::table('watches_fech_queue')
                            ->where('id', $item->id)
                            ->update([
                                'status' => 2,
                                'error_message' => '时研家网站没有此型号的产品'
                            ]);
                    }
                    else
                    {
                        $link = "{$this->baseUrl}{$href}"; 
                        $htmlContent = $this->scrapeWebsite($browser, $link, '', 3000);

                        $ql = QueryList::html($htmlContent);
                        $used_market_price = $ql->find('.detail-top-right h3:last')->html();
                        $elements = $ql->find('.detail-top-right span');
                        $change_percent = $elements->eq($elements->count() - 2)->html();
                        dump($used_market_price, $change_percent);
                        
                        if(!empty($used_market_price) || !empty($change_percent))
                        {
                            // DB::table('watches_quote')->insert([
                            //     'watches_id' => $item->watches_id,
                            //     'used_market_price' => trim($used_market_price, '¥'), // 过滤掉¥
                            //     'change_percent' => trim($change_percent, '%'), // 过滤掉%
                            //     'create_time' => date('Y-m-d H:i:s')
                            // ]);

                            DB::table('watches_fech_queue')
                                ->where('id', $item->id)
                                ->update([
                                    'status' => 1,
                                    'fetch_time' => date('Y-m-d H:i:s')
                                ]);
                        }
                        else
                        {   
                            DB::table('watches_fech_queue')
                                ->where('id', $item->id)
                                ->update([
                                    'status' => 2,
                                    'error_message' => '时研家网站有此型号，但无二手行情价格'
                                ]);
                        }
                    }
                } 
                catch (\Throwable $e) 
                {
                    $this->info('程序异常：' . $e->getMessage());
                    DB::table('watches_fech_queue')
                        ->where('id', $item->id)
                        ->update([
                            'status' => 3,
                            'error_message' => $e->getMessage()
                        ]);
                }
                finally {
                    // 释放内存
                    $ql->destruct();
                    unset($ql, $htmlContent, $href, $link, $used_market_price, $change_percent);
                    libxml_clear_errors();
                }
            }
        }
        catch (\Throwable $e) 
        {
            $this->info('程序异常：' . $e->getMessage());
        } 
        finally {
            // 释放内存
            if (isset($browser)) {
                $browser->close();
            }
            DB::disconnect();
            unset($browser, $queueItems);
        }
    }

    /**
     * 使用无头浏览器抓取网页内容
     */
    private function scrapeWebsite($browser, $url, $waitElement = '', $waitTime = 15000): string
    {
        try {

            $page = $browser->createPage();
            $page->navigate($url)->waitForNavigation();
            
            if($waitElement) {
                $page->waitUntilContainsElement($waitElement, $waitTime);
            } else {
                usleep($waitTime);
            }
            
            return $page->getHtml();
        }
        catch (\Throwable $e) 
        {
            return '<html><head></head><body></body></html>';
        } 
        finally 
        {
            if (isset($page)) {
                $page->close();
            }
        }
    }
} 