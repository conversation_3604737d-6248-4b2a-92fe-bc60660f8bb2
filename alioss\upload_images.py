#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import configparser
import pymysql
import logging
from concurrent.futures import ThreadPoolExecutor
import oss2
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("upload.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("OSS_Upload")

class ImageUploader:
    def __init__(self, config_file="config.ini"):
        self.config = configparser.ConfigParser()
        self.config.read(config_file, encoding='utf-8')
        
        # 阿里云OSS配置
        self.access_key_id = self.config.get('AliyunOSS', 'access_key_id')
        self.access_key_secret = self.config.get('AliyunOSS', 'access_key_secret')
        self.endpoint = self.config.get('AliyunOSS', 'endpoint')
        self.bucket_name = self.config.get('AliyunOSS', 'bucket_name')
        self.oss_prefix = self.config.get('AliyunOSS', 'oss_prefix')
        
        # 数据库配置
        self.db_host = self.config.get('Database', 'host')
        self.db_port = self.config.getint('Database', 'port')
        self.db_user = self.config.get('Database', 'user')
        self.db_password = self.config.get('Database', 'password')
        self.db_name = self.config.get('Database', 'database')
        self.table_name = self.config.get('Database', 'table_name')
        
        # 任务配置
        self.batch_size = self.config.getint('Task', 'batch_size')
        self.max_workers = self.config.getint('Task', 'max_workers')
        self.checkpoint_file = self.config.get('Task', 'checkpoint_file')
        self.retry_attempts = self.config.getint('Task', 'retry_attempts')
        
        # 初始化OSS客户端
        self.auth = oss2.Auth(self.access_key_id, self.access_key_secret)
        self.bucket = oss2.Bucket(self.auth, self.endpoint, self.bucket_name)
        
        # 记录处理进度
        self.last_id = self._get_last_processed_id()
        
    def _get_last_processed_id(self):
        """从checkpoint文件中获取上次处理到的ID"""
        if os.path.exists(self.checkpoint_file):
            with open(self.checkpoint_file, 'r') as f:
                try:
                    return int(f.read().strip())
                except:
                    return 0
        return 0
    
    def _save_checkpoint(self, last_id):
        """保存处理进度到checkpoint文件"""
        with open(self.checkpoint_file, 'w') as f:
            f.write(str(last_id))
        self.last_id = last_id
    
    def _get_db_connection(self):
        """获取数据库连接"""
        return pymysql.connect(
            host=self.db_host,
            port=self.db_port,
            user=self.db_user,
            password=self.db_password,
            database=self.db_name,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
    
    def _get_image_batch(self, conn):
        """获取一批需要处理的图片记录"""
        with conn.cursor() as cursor:
            sql = f"""
                SELECT id, local_url 
                FROM {self.table_name} 
                WHERE id > %s 
                ORDER BY id ASC 
                LIMIT %s
            """
            cursor.execute(sql, (self.last_id, self.batch_size))
            return cursor.fetchall()
    
    def _upload_image(self, image_record):
        """上传单张图片到OSS并更新数据库"""
        image_id = image_record['id']
        local_url = image_record['local_url']

        # 重置本地路径
        local_url = f"../storage/app/{local_url}"
        
        if not local_url or not os.path.exists(local_url):
            logger.warning(f"图片不存在: ID={image_id}, 路径={local_url}")
            return False
        
        # 获取文件名并生成OSS路径
        filename = os.path.basename(local_url)
        oss_path = f"{self.oss_prefix}{filename}"
        
        try:
            # 上传文件到OSS
            for attempt in range(self.retry_attempts):
                try:
                    result = self.bucket.put_object_from_file(oss_path, local_url)
                    if result.status == 200:
                        break
                except Exception as e:
                    if attempt < self.retry_attempts - 1:
                        time.sleep(2)  # 上传失败后等待一段时间再重试
                        continue
                    raise e
            
            # 生成OSS URL
            oss_url = f"https://{self.bucket_name}.{self.endpoint}/{oss_path}"
            
            # 更新数据库
            conn = self._get_db_connection()
            try:
                with conn.cursor() as cursor:
                    sql = f"""
                        UPDATE {self.table_name} 
                        SET local_url = %s, status = 1 
                        WHERE id = %s
                    """
                    cursor.execute(sql, (oss_url, image_id))
                conn.commit()
                logger.info(f"成功上传并更新: ID={image_id}, URL={oss_url}")
                return True
            except Exception as e:
                conn.rollback()
                logger.error(f"更新数据库失败: ID={image_id}, 错误={str(e)}")
                return False
            finally:
                conn.close()
                
        except Exception as e:
            logger.error(f"上传失败: ID={image_id}, 路径={local_url}, 错误={str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def run(self):
        """启动上传任务"""
        logger.info(f"开始上传任务, 从ID {self.last_id} 继续处理")
        
        while True:
            try:
                conn = self._get_db_connection()
                batch = self._get_image_batch(conn)
                conn.close()
                
                if not batch:
                    logger.info("没有更多图片需要处理，任务完成")
                    break
                
                logger.info(f"获取到 {len(batch)} 条记录，开始处理...")
                
                # 使用线程池并行处理图片上传
                with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    executor.map(self._upload_image, batch)
                
                # 更新checkpoint
                last_id = batch[-1]['id']
                self._save_checkpoint(last_id)
                logger.info(f"批次处理完成，当前进度: ID={last_id}")
                
                # 避免请求过于频繁
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"处理批次时发生错误: {str(e)}")
                logger.error(traceback.format_exc())
                time.sleep(10)  # 出错后等待一段时间再继续

if __name__ == "__main__":
    uploader = ImageUploader()
    uploader.run() 