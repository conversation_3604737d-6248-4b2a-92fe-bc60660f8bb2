#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import wave
import requests
from flask import Flask, request, jsonify, after_this_request
from werkzeug.utils import secure_filename
from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.request import CommonRequest
from config import (
    ACCESS_KEY_ID,
    ACCESS_KEY_SECRET,
    APP_KEY,
    REGION_ID,
    ENDPOINT,
    DEFAULT_SAMPLE_RATE
)

class CustomJSONEncoder(json.JSONEncoder):
    def __init__(self, *args, **kwargs):
        kwargs['ensure_ascii'] = False
        super(CustomJSONEncoder, self).__init__(*args, **kwargs)

app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False  # 确保JSON响应中的中文字符正确显示，不会被转为ASCII编码
app.json_encoder = CustomJSONEncoder  # 使用自定义的JSON编码器

# 设置响应头的装饰器
@app.after_request
def add_charset_header(response):
    if response.mimetype == 'application/json':
        response.headers['Content-Type'] = 'application/json; charset=utf-8'
    return response

# 配置上传文件夹
UPLOAD_FOLDER = './uploads'
ALLOWED_EXTENSIONS = {'wav', 'mp3', 'pcm'}

if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 限制上传大小为16MB

def allowed_file(filename):
    """检查文件扩展名是否合法"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_token():
    """获取阿里云语音识别服务的访问令牌"""
    try:
        # 创建AcsClient实例
        client = AcsClient(ACCESS_KEY_ID, ACCESS_KEY_SECRET, REGION_ID)
        
        # 创建请求
        request = CommonRequest()
        request.set_domain(ENDPOINT)
        request.set_version('2019-02-28')
        request.set_action_name('CreateToken')
        request.set_method('POST')
        
        # 发送请求
        response = client.do_action_with_exception(request)
        response_json = json.loads(response.decode('utf-8'))
        
        # 提取令牌
        if 'Token' in response_json and 'Id' in response_json['Token']:
            return response_json['Token']['Id']
        else:
            app.logger.error(f"获取令牌失败: {response_json}")
            return None
    except Exception as e:
        app.logger.error(f"获取令牌异常: {str(e)}")
        return None

def recognize_speech(audio_file_path):
    """
    使用阿里云语音识别服务识别音频文件
    
    参数:
        audio_file_path: 音频文件路径
        
    返回:
        识别出的文本或None（如果识别失败）
    """
    if not os.path.exists(audio_file_path):
        app.logger.error(f"错误: 文件不存在 - {audio_file_path}")
        return None
    
    # 获取阿里云访问令牌
    token = get_token()
    if not token:
        app.logger.error("错误: 无法获取阿里云访问令牌")
        return None
    
    # 读取音频文件
    with open(audio_file_path, 'rb') as f:
        audio_content = f.read()
    
    # 获取文件扩展名
    file_ext = os.path.splitext(audio_file_path)[1].lower()
    if file_ext not in ['.wav', '.pcm', '.mp3']:
        app.logger.error(f"错误: 不支持的音频格式 - {file_ext}")
        return None
    
    # 构建请求
    url = "https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/asr"
    
    headers = {
        "X-NLS-Token": token,
        "Content-Type": "application/octet-stream",
        "Content-Length": str(len(audio_content)),
        "Host": "nls-gateway.cn-shanghai.aliyuncs.com"
    }
    
    params = {
        "appkey": APP_KEY,
        "format": file_ext.replace(".", ""),
        "sample_rate": DEFAULT_SAMPLE_RATE,
        "enable_punctuation_prediction": True,
        "enable_inverse_text_normalization": True
    }
    
    # 发送请求
    try:
        app.logger.info("正在发送识别请求...")
        response = requests.post(url, params=params, headers=headers, data=audio_content)
        
        # 确保响应内容以UTF-8解码
        response.encoding = 'utf-8'
        result = response.json()
        
        # 记录原始结果以便调试
        app.logger.info(f"原始识别结果: {json.dumps(result, ensure_ascii=False)}")
        
        # 处理结果
        if response.status_code == 200 and result.get("status") == 20000000:
            recognized_text = result.get("result", "")
            app.logger.info(f"成功识别文本: {recognized_text}")
            return recognized_text
        else:
            app.logger.error(f"识别失败: {json.dumps(result, ensure_ascii=False)}")
            return None
    except Exception as e:
        app.logger.error(f"请求异常: {str(e)}")
        return None

def recognize_from_url(audio_url):
    """
    从URL下载音频文件并进行识别
    
    参数:
        audio_url: 音频文件URL
        
    返回:
        识别出的文本或None（如果识别失败）
    """
    try:
        # 从URL获取文件名和扩展名
        filename = os.path.basename(audio_url)
        if not any(filename.lower().endswith(ext) for ext in ALLOWED_EXTENSIONS):
            app.logger.error(f"不支持的文件类型: {filename}")
            return None, "不支持的文件类型，请提供 wav, mp3 或 pcm 格式的URL"
        
        # 下载文件
        app.logger.info(f"正在从URL下载: {audio_url}")
        response = requests.get(audio_url, stream=True)
        if response.status_code != 200:
            return None, f"无法下载文件，状态码: {response.status_code}"
        
        # 保存到临时文件
        temp_filename = secure_filename(filename)
        temp_filepath = os.path.join(app.config['UPLOAD_FOLDER'], temp_filename)
        
        with open(temp_filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        # 进行识别
        text = recognize_speech(temp_filepath)
        
        # 删除临时文件
        try:
            os.remove(temp_filepath)
        except Exception as e:
            app.logger.warning(f"删除临时文件失败: {str(e)}")
        
        if text:
            return text, None
        else:
            return None, "语音识别失败"
    except Exception as e:
        app.logger.error(f"处理URL异常: {str(e)}")
        return None, f"处理异常: {str(e)}"

@app.route('/recognize', methods=['POST'])
def recognize_endpoint():
    """接收文件并返回识别结果"""
    # 检查是否有文件
    if 'file' not in request.files:
        response = json.dumps({"error": "未找到文件"}, ensure_ascii=False)
        return app.response_class(response=response, status=400, mimetype='application/json')
    
    file = request.files['file']
    
    # 检查文件名是否为空
    if file.filename == '':
        response = json.dumps({"error": "未选择文件"}, ensure_ascii=False)
        return app.response_class(response=response, status=400, mimetype='application/json')
    
    # 检查文件扩展名是否合法
    if not allowed_file(file.filename):
        response = json.dumps({"error": f"不支持的文件类型，请上传 {', '.join(ALLOWED_EXTENSIONS)} 格式"}, ensure_ascii=False)
        return app.response_class(response=response, status=400, mimetype='application/json')
    
    # 保存文件
    filename = secure_filename(file.filename)
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    file.save(filepath)
    
    # 进行识别
    text = recognize_speech(filepath)
    
    # 删除临时文件
    try:
        os.remove(filepath)
    except Exception as e:
        app.logger.warning(f"删除临时文件失败: {str(e)}")
    
    # 返回结果
    if text:
        response = json.dumps({"content": text}, ensure_ascii=False)
        return app.response_class(response=response, status=200, mimetype='application/json')
    else:
        response = json.dumps({"error": "语音识别失败"}, ensure_ascii=False)
        return app.response_class(response=response, status=500, mimetype='application/json')

@app.route('/recognize_local', methods=['GET'])
def recognize_local_file():
    """识别本地的1.mp3文件"""
    audio_file = "1.mp3"
    
    # 检查文件是否存在
    if not os.path.exists(audio_file):
        response = json.dumps({"error": f"本地文件 {audio_file} 不存在"}, ensure_ascii=False)
        return app.response_class(response=response, status=404, mimetype='application/json')
    
    # 进行识别
    text = recognize_speech(audio_file)
    
    # 返回结果
    if text:
        response = json.dumps({"content": text}, ensure_ascii=False)
        return app.response_class(response=response, status=200, mimetype='application/json')
    else:
        response = json.dumps({"error": "语音识别失败"}, ensure_ascii=False)
        return app.response_class(response=response, status=500, mimetype='application/json')

@app.route('/recognize_url', methods=['GET'])
def recognize_url_file():
    """识别远程URL指向的音频文件"""
    # 获取URL参数
    url = request.args.get('url')
    
    if not url:
        response = json.dumps({"error": "缺少url参数"}, ensure_ascii=False)
        return app.response_class(response=response, status=400, mimetype='application/json')
    
    # 从URL下载并识别
    text, error = recognize_from_url(url)
    
    # 返回结果
    if text:
        # response = json.dumps({"code": 0, "msg": "success", "data": {"content": text}}, ensure_ascii=False)
        return app.response_class(response=text, status=200, mimetype='application/json')
    else:
        # response = json.dumps({"code": 1, "msg": error or "语音识别失败"}, ensure_ascii=False)
        return app.response_class(response=error, status=500, mimetype='application/json')

if __name__ == "__main__":
    app.run(host='0.0.0.0', port=5000, debug=False, threaded=True) 