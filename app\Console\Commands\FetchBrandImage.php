<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use QL\QueryList;
use GuzzleHttp\Client;
use HeadlessChromium\BrowserFactory;

class FetchBrandImage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fetch-brand-image';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '采集品牌的图片';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 批量获取需要采集的数据
        $browserFactory = new BrowserFactory();
        $browser = $browserFactory->createBrowser([
            'headless' => false,
            'connectionDelay' => 0.8,
            'windowSize' => [1920, 1080],
        ]);

        try {

            $html = $this->scrapeWebsite($browser, 'https://www.rolex.com/zh-hans/search?q=m228349rbr-0040', '.watches img', 15000);

            $images = QueryList::html($html)
                ->find('.watches')
                ->find('img')
                ->attr('srcset');
                // ->map(function ($img) {
                //     return $img->attr('srcset') ?: $img->attr('src'); // 同时尝试 src 属性
                // })->all();
            dd($images);
            $images = explode(' ', $images);
            file_put_contents('1.png', $images[4]);

            // 使用file_put_contents下载图片$images[4]到本地
            file_put_contents('1.png', file_get_contents($images[4]));
        } finally {
            $browser->close();
        }
    }

    /**
     * 使用无头浏览器抓取网页内容
     */
    private function scrapeWebsite($browser, $url, $waitElement = '', $waitTime = 15000): string
    {
        try {

            $page = $browser->createPage();
            $page->navigate($url)->waitForNavigation();
            
            if($waitElement) {
                $page->waitUntilContainsElement($waitElement, $waitTime);
            } else {
                usleep($waitTime);
            }

            $html = $page->evaluate('document.documentElement.outerHTML')->getReturnValue();

            return $html;
        }
        catch (\Throwable $e) 
        {
            $this->info("页面加载失败：" . $e->getMessage());
            return '';
        } 
        finally 
        {
            if (isset($page)) {
                $page->close();
            }
        }
    }
} 