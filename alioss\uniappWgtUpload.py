#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import configparser
import logging
import oss2
import traceback

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("wgt_upload.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("WGT_Upload")

class WgtUploader:
    def __init__(self, config_file="config.ini"):
        self.config = configparser.ConfigParser()
        self.config.read(config_file, encoding='utf-8')
        
        # Aliyun OSS configuration
        self.access_key_id = self.config.get('AliyunOSS', 'access_key_id')
        self.access_key_secret = self.config.get('AliyunOSS', 'access_key_secret')
        self.endpoint = self.config.get('AliyunOSS', 'endpoint')
        self.bucket_name = self.config.get('AliyunOSS', 'bucket_name')
        self.oss_prefix = self.config.get('AliyunOSS', 'oss_prefix')
        
        # WGT file configuration
        self.local_wgt_path = "D:\\phpEnv\\www\\watches-app\\unpackage\\release\\107.wgt"  # Path to the local wgt file
        
        # Initialize OSS client
        self.auth = oss2.Auth(self.access_key_id, self.access_key_secret)
        self.bucket = oss2.Bucket(self.auth, self.endpoint, self.bucket_name)
        
    def upload_wgt(self):
        """Upload the WGT file to OSS"""
        if not os.path.exists(self.local_wgt_path):
            logger.error(f"WGT file not found at: {self.local_wgt_path}")
            return False
        
        # Generate the OSS object key
        filename = os.path.basename(self.local_wgt_path)
        oss_path = f"{self.oss_prefix}{filename}"
        
        try:
            # Upload file to OSS
            logger.info(f"Uploading {self.local_wgt_path} to OSS...")
            result = self.bucket.put_object_from_file(oss_path, self.local_wgt_path)
            
            if result.status == 200:
                # Generate OSS URL
                oss_url = f"https://{self.bucket_name}.{self.endpoint}/{oss_path}"
                logger.info(f"Upload successful! File available at: {oss_url}")
                print(f"OSS URL: {oss_url}")
                return oss_url
            else:
                logger.error(f"Upload failed with status code: {result.status}")
                return False
                
        except Exception as e:
            logger.error(f"Upload failed: {str(e)}")
            logger.error(traceback.format_exc())
            return False

if __name__ == "__main__":
    uploader = WgtUploader()
    uploader.upload_wgt() 