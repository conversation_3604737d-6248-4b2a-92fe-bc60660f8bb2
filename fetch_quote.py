#!/usr/bin/env python3
import os
import time
import random
import argparse
import requests
import urllib3
from bs4 import BeautifulSoup
import re
import logging
import sys
import traceback
import platform
import codecs

# 禁用不安全请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class FetchQuote:
    def __init__(self, limit=50, sleep=2, use_proxy=True):
        self.limit = limit
        self.sleep_time = sleep
        self.use_proxy = use_proxy
        self.base_url = 'https://www.timez.cn'
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
        ]
        
        # 检查是否在dump模式下运行（不使用数据库）
        self.dump_mode = os.environ.get('DUMP_MODE', 'false').lower() == 'true'
        
        # 配置日志 - 同时输出到文件和控制台
        log_dir = os.path.dirname(os.path.abspath(__file__))
        log_file = os.path.join(log_dir, 'fetch_quote.log')
        
        # 创建一个处理器，用于写入日志文件
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 创建一个处理器，用于输出到控制台
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 定义handler的输出格式
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 设置根日志记录器
        logging.basicConfig(level=logging.INFO, handlers=[file_handler, console_handler])
        self.logger = logging.getLogger(__name__)
        
        # 如果是dump模式，创建CSV文件用于输出数据
        if self.dump_mode:
            self.logger.info("以DUMP模式运行，数据将保存到CSV文件")
            self.setup_dump_mode()
            return
        
        # 设置数据库连接
        try:
            # 确保在此作用域内导入mysql.connector模块
            import mysql.connector
            from mysql.connector import Error
            
            self.db_config = {
                'host': os.environ.get('DB_HOST', '************'),
                'user': os.environ.get('DB_USERNAME', 'wxx'),
                'password': os.environ.get('DB_PASSWORD', 'YtjenCbTz3cd7anW'),
                'database': os.environ.get('DB_DATABASE', 'ry-vue'),
            }
            self.logger.info(f"连接数据库配置: {self.db_config['host']}, 用户: {self.db_config['user']}, 数据库: {self.db_config['database']}")
            self.logger.info(f"运行环境: {platform.system()} {platform.release()}")
            
            try:
                # 测试端口连通性
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((self.db_config['host'], 3306))
                if result == 0:
                    self.logger.info("数据库端口 3306 连接成功")
                else:
                    self.logger.error(f"数据库端口 3306 连接失败，错误码: {result}")
                sock.close()
            except Exception as socket_err:
                self.logger.error(f"测试数据库端口连接时出错: {socket_err}")
            
            # 在Windows上检查MySQL客户端配置
            if platform.system() == 'Windows':
                self.logger.info("Windows环境：检查MySQL配置")
                try:
                    # 检查是否有安装MySQL客户端
                    mysql_client_path = None
                    import subprocess
                    try:
                        mysql_client_path = subprocess.check_output("where mysql", shell=True, text=True).strip()
                        self.logger.info(f"检测到MySQL客户端: {mysql_client_path}")
                    except subprocess.CalledProcessError:
                        self.logger.warning("未找到MySQL客户端安装")
                    
                    # 获取MySQL Connector版本信息
                    self.logger.info(f"MySQL Connector版本: {mysql.connector.__version__}")
                    # 获取MySQL协议版本信息
                    self.logger.info(f"MySQL协议版本: {mysql.connector.constants.MAX_MYSQL_TABLE_COLUMNS}")
                except Exception as config_err:
                    self.logger.error(f"检查MySQL配置时出错: {config_err}")
                
            self.logger.info("正在连接数据库...")
            try:
                # 记录更详细的连接过程
                self.logger.info("连接参数:")
                # 打印除密码外的所有配置
                safe_config = self.db_config.copy()
                safe_config['password'] = '******'  # 隐藏密码
                for key, value in safe_config.items():
                    self.logger.info(f"  {key}: {value}")
                
                self.logger.info("尝试建立数据库连接...")
                # 设置详细的连接选项
                conn_options = {
                    'connect_timeout': 30,  # 连接超时设置为30秒
                    'use_pure': True,       # 使用纯Python实现
                    'autocommit': True,     # 自动提交
                    'pool_size': 5,         # 连接池大小
                    'pool_name': 'mypool',  # 连接池名称
                    'ssl_disabled': True    # 禁用SSL
                }
                
                # 将这些选项添加到配置中
                config = self.db_config.copy()
                config.update(conn_options)
                
                self.logger.info("连接中...")
                self.conn = mysql.connector.connect(**config)
                self.logger.info("连接建立成功！")
                
                self.logger.info("创建游标...")
                self.cursor = self.conn.cursor(dictionary=True)
                self.logger.info("游标创建成功")
                
                # 测试数据库连接
                self.logger.info("测试执行简单查询...")
                self.cursor.execute("SELECT 1")
                result = self.cursor.fetchone()
                self.logger.info(f"查询测试结果: {result}")
                
                self.logger.info("数据库连接成功并验证")
            except mysql.connector.Error as mysql_err:
                self.logger.error(f"MySQL连接错误: {mysql_err}")
                self.logger.error(f"MySQL错误代码: {mysql_err.errno}")
                self.logger.error(f"MySQL错误消息: {mysql_err.msg}")
                self.logger.error(f"MySQL SQL状态: {mysql_err.sqlstate}")
                
                # 根据错误代码提供具体建议
                if mysql_err.errno == 1045:  # 访问被拒绝，通常是用户名或密码错误
                    self.logger.error("用户名或密码错误，请检查认证信息")
                elif mysql_err.errno == 2003:  # 无法连接到MySQL服务器
                    self.logger.error("无法连接到MySQL服务器，可能是服务器未运行或网络问题")
                elif mysql_err.errno == 1044:  # 数据库访问被拒绝
                    self.logger.error("没有访问该数据库的权限")
                elif mysql_err.errno == 1049:  # 未知数据库
                    self.logger.error("指定的数据库不存在")
                elif mysql_err.errno == 2005:  # 未知主机
                    self.logger.error("服务器主机无法解析，请检查主机名")
                elif mysql_err.errno == 2013:  # 连接中断
                    self.logger.error("连接过程中断，可能是网络不稳定或服务器超时")
                
                raise
            except Exception as general_err:
                self.logger.error(f"一般错误: {general_err}")
                self.logger.error(f"错误类型: {type(general_err).__name__}")
                self.logger.error(traceback.format_exc())
                raise
        except Exception as e:
            self.logger.error(f"数据库连接错误: {e}")
            if platform.system() == 'Windows':
                self.logger.error("Windows 环境下数据库连接失败，可能原因：")
                self.logger.error("1. 数据库服务器可能限制了 Windows IP 访问")
                self.logger.error("2. Windows 防火墙阻止了数据库连接")
                self.logger.error("3. MySQL Connector 版本兼容性问题")
                self.logger.error("4. 网络连接问题")
                
                # 尝试获取更多错误信息
                try:
                    import mysql.connector
                    self.logger.error(f"MySQL Connector 版本: {mysql.connector.__version__}")
                except Exception as mysql_err:
                    self.logger.error(f"无法导入MySQL Connector模块: {mysql_err}")
                    self.logger.error("请确保已安装mysql-connector-python: pip install mysql-connector-python")
                
                # 执行网络诊断
                self.logger.info("执行网络诊断...")
                self.diagnose_network(self.db_config['host'])
                
                self.logger.info("建议尝试以下解决方案:")
                self.logger.info("1. 使用 --local-db 参数使用本地数据库")
                self.logger.info("2. 检查Windows防火墙设置，允许MySQL连接")
                self.logger.info("3. 在Windows上安装与服务器兼容的MySQL版本")
                self.logger.info("4. 使用VPN或代理连接到数据库服务器")
            raise

    def __del__(self):
        """清理资源"""
        try:
            if hasattr(self, 'cursor') and self.cursor:
                self.cursor.close()
            if hasattr(self, 'conn') and self.conn:
                self.conn.close()
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"Error in cleanup: {e}")

    def get_request_headers(self):
        """生成带有随机用户代理的请求头"""
        return {
            'X-Forwarded-For': None,
            'X-Real-IP': None,
            'Forwarded': None,
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-encoding': 'gzip, deflate, br',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'cache-control': 'no-cache',
            'connection': 'keep-alive',
            'origin': 'https://www.timez.cn',
            'priority': 'u=0, i',
            'pragma': 'no-cache',
            'referer': 'https://www.timez.cn/',
            'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-storage-access': 'active',
            'user-agent': random.choice(self.user_agents),
        }

    def get_watches_count(self):
        """统计需要处理的记录总数"""
        if self.dump_mode:
            return len(self.watches_to_process)
            
        try:
            self.cursor.execute("SELECT COUNT(*) as count FROM watches WHERE is_quote = 0")
            result = self.cursor.fetchone()
            return result['count']
        except Exception as e:
            self.logger.error(f"Database query error: {e}")
            return 0

    def get_watches_batch(self, page):
        """获取一批is_quote = 0的手表记录"""
        if self.dump_mode:
            # 在dump模式下，直接从内存中获取数据
            start_idx = (page - 1) * self.limit
            end_idx = min(start_idx + self.limit, len(self.watches_to_process))
            return self.watches_to_process[start_idx:end_idx]
            
        try:
            offset = (page - 1) * self.limit
            query = f"""
                SELECT * FROM watches 
                WHERE is_quote = 0
                ORDER BY id
                LIMIT {self.limit} OFFSET {offset}
            """
            self.cursor.execute(query)
            return self.cursor.fetchall()
        except Exception as e:
            self.logger.error(f"Database query error: {e}")
            return []

    def clean_reference_number(self, reference_number):
        """清理参考编号"""
        if reference_number is None:
            return ""
        reference_number = reference_number.replace('）', '').replace('（', '')
        reference_number = reference_number.replace('"', '').replace('-', '')
        # 移除中文字符
        reference_number = re.sub(r'[\u4e00-\u9fff]+', '', reference_number)
        return reference_number.strip()

    def update_watch_status(self, watch_id):
        """更新手表状态为is_quote = 1"""
        try:
            query = "UPDATE watches SET is_quote = 1 WHERE id = %s"
            self.cursor.execute(query, (watch_id,))
            self.conn.commit()
        except Exception as e:
            self.logger.error(f"Database update error: {e}")
            self.conn.rollback()

    def insert_quote(self, watch_id, used_market_price, change_percent):
        """将报价数据插入数据库"""
        try:
            # 移除货币符号和百分号
            used_market_price = used_market_price.replace('¥', '').strip()
            change_percent = change_percent.replace('%', '').strip()
            
            query = """
                INSERT INTO watches_quote 
                (watches_id, used_market_price, change_percent) 
                VALUES (%s, %s, %s)
            """
            self.cursor.execute(query, (watch_id, used_market_price, change_percent))
            self.conn.commit()

            #打印编号
            self.logger.info(f"编号: {watch_id}")
            
        except Exception as e:
            self.logger.error(f"Database insert error: {e}")
            self.conn.rollback()

    def check_proxy_connection(self):
        """检查代理连接是否可用"""
        if not self.use_proxy:
            return False
            
        try:
            # 从环境变量获取代理检测地址，如果未设置则使用默认值
            proxy_check_address = os.environ.get('PROXY_CHECK_ADDRESS', 'http://127.0.0.1:7897')
            requests.get('http://www.baidu.com', proxies={
                'http': proxy_check_address,
                'https': proxy_check_address
            }, timeout=5, verify=False)
            return True
        except:
            return False

    def diagnose_network(self, host):
        """诊断网络连接问题"""
        self.logger.info(f"开始诊断网络连接到 {host}...")
        
        # 检查DNS解析
        try:
            import socket
            ip = socket.gethostbyname(host)
            self.logger.info(f"DNS解析: {host} -> {ip}")
        except Exception as e:
            self.logger.error(f"DNS解析失败: {e}")
            return
        
        # 尝试ping
        try:
            import subprocess
            if platform.system() == 'Windows':
                ping_cmd = ['ping', '-n', '4', host]
            else:
                ping_cmd = ['ping', '-c', '4', host]
            
            self.logger.info(f"尝试Ping测试: {' '.join(ping_cmd)}")
            result = subprocess.run(ping_cmd, capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info("Ping成功")
                self.logger.info(result.stdout)
            else:
                self.logger.error("Ping失败")
                self.logger.error(result.stderr)
        except Exception as e:
            self.logger.error(f"执行Ping测试时出错: {e}")
        
        # 尝试路由追踪
        try:
            if platform.system() == 'Windows':
                tracert_cmd = ['tracert', '-d', '-h', '15', host]
            else:
                tracert_cmd = ['traceroute', '-n', '-m', '15', host]
            
            self.logger.info(f"尝试路由追踪: {' '.join(tracert_cmd)}")
            result = subprocess.run(tracert_cmd, capture_output=True, text=True)
            self.logger.info("路由追踪结果:")
            self.logger.info(result.stdout)
        except Exception as e:
            self.logger.error(f"执行路由追踪时出错: {e}")
            
        # Windows特定的网络检查
        if platform.system() == 'Windows':
            self.check_windows_network_settings(host)
            
    def check_windows_network_settings(self, host):
        """检查Windows特定的网络设置和限制"""
        self.logger.info("检查Windows网络配置...")
        
        try:
            import subprocess
            
            # 检查防火墙状态
            self.logger.info("检查Windows防火墙状态...")
            firewall_cmd = ["netsh", "advfirewall", "show", "currentprofile"]
            result = subprocess.run(firewall_cmd, capture_output=True, text=True)
            if "State                      ON" in result.stdout:
                self.logger.warning("Windows防火墙已启用，可能需要添加例外规则")
                self.logger.info(result.stdout)
            else:
                self.logger.info("Windows防火墙未启用或已配置")
                
            # 检查网络接口配置
            self.logger.info("检查网络接口配置...")
            ipconfig_cmd = ["ipconfig", "/all"]
            result = subprocess.run(ipconfig_cmd, capture_output=True, text=True)
            self.logger.info("网络接口配置:")
            self.logger.info(result.stdout)
            
            # 检查到主机的路由
            self.logger.info(f"检查到{host}的路由...")
            route_cmd = ["route", "print", host]
            result = subprocess.run(route_cmd, capture_output=True, text=True)
            self.logger.info("路由信息:")
            self.logger.info(result.stdout)
            
            # 检查TLS/SSL支持
            self.logger.info("检查TLS/SSL支持...")
            try:
                import ssl
                context = ssl.create_default_context()
                self.logger.info(f"SSL版本: {ssl.OPENSSL_VERSION}")
                self.logger.info(f"支持的SSL协议: {context.options}")
            except Exception as ssl_err:
                self.logger.error(f"检查SSL支持时出错: {ssl_err}")
                
        except Exception as e:
            self.logger.error(f"检查Windows网络设置时出错: {e}")

    def setup_dump_mode(self):
        """设置转储模式，在不连接数据库的情况下运行"""
        import csv
        import datetime
        
        # 创建CSV文件
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.csv_filename = f"watches_dump_{timestamp}.csv"
        
        self.logger.info(f"创建CSV文件: {self.csv_filename}")
        
        # 创建CSV文件并写入表头
        with open(self.csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['reference_number', 'used_market_price', 'change_percent', 'detail_url', 'timestamp']
            self.csv_writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            self.csv_writer.writeheader()
        
        # 创建一个内存列表用于存储待处理的手表
        self.watches_to_process = []
        
        # 添加一些示例数据用于测试
        self.add_sample_watches()
        
        self.logger.info(f"Dump模式初始化完成，将处理 {len(self.watches_to_process)} 个样本")
    
    def add_sample_watches(self):
        """添加示例手表数据用于测试"""
        # 添加一些常见的手表型号作为示例
        sample_watches = [
            {"id": 1, "reference_number": "126610LN"},  # 劳力士潜航者
            {"id": 2, "reference_number": "126710BLRO"},  # 劳力士格林尼治
            {"id": 3, "reference_number": "5711/1A-010"},  # 百达翡丽鹦鹉螺
            {"id": 4, "reference_number": "15202ST.OO.1240ST.01"},  # 爱彼皇家橡树
            {"id": 5, "reference_number": "324SC/6/9067R"},  # 百达翡丽古典系列
            {"id": 6, "reference_number": "116500LN"},  # 劳力士迪通拿
            {"id": 7, "reference_number": "26470ST.OO.A027CA.01"},  # 爱彼皇家橡树离岸型
            {"id": 8, "reference_number": "5164A-001"},  # 百达翡丽AQUANAUT TRAVEL TIME
            {"id": 9, "reference_number": "126234"},  # 劳力士日志型
            {"id": 10, "reference_number": "228238"},  # 劳力士星期日历型
        ]
        
        self.watches_to_process = sample_watches
        
    def save_to_csv(self, reference_number, used_market_price, change_percent, detail_url):
        """将数据保存到CSV文件"""
        import datetime
        import csv
        
        with open(self.csv_filename, 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=['reference_number', 'used_market_price', 'change_percent', 'detail_url', 'timestamp'])
            writer.writerow({
                'reference_number': reference_number,
                'used_market_price': used_market_price,
                'change_percent': change_percent,
                'detail_url': detail_url,
                'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
        
        self.logger.info(f"数据已保存到CSV: {reference_number}, 价格: {used_market_price}, 变化: {change_percent}")

    def process_watch(self, watch):
        """处理单个手表记录"""
        watch_id = watch['id']
        reference_number = watch.get('reference_number', '')
        cleaned_number = self.clean_reference_number(reference_number)
        
        if not cleaned_number:
            self.logger.warning(f"Empty reference number for watch ID: {watch_id}")
            return
            
        self.logger.info(f"Processing watch: {reference_number}")
        
        # 构建URL
        url = f"{self.base_url}/search/{cleaned_number}"
        
        try:
            # 添加延迟以避免被封锁
            time.sleep(random.randint(1, self.sleep_time))
            
            # 创建会话以保留cookies
            session = requests.Session()
            
            # 检查代理是否可用
            proxy_available = self.check_proxy_connection()
            if proxy_available:
                # 从环境变量获取代理地址，如果未设置则使用默认值
                proxy_address = os.environ.get('HTTP_PROXY', 'http://127.0.0.1:7890')
                session.proxies = {
                    'http': proxy_address,
                    'https': proxy_address
                }
                self.logger.info(f"Using proxy for requests: {proxy_address}")
            else:
                self.logger.warning("Not using proxy for requests")
            
            # 获取搜索页面
            response = session.get(url, headers=self.get_request_headers(), verify=False, timeout=30)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找第一个结果链接
            first_result = soup.select_one('.watch-list-wrap a')
            if not first_result:
                self.logger.info(f"No search results for {cleaned_number}")
                return
                
            # 获取详情页URL
            detail_url = f"{self.base_url}{first_result['href']}"
            self.logger.info(f"Found detail page: {detail_url}")
            
            # 为详情页请求添加另一个延迟
            time.sleep(random.randint(1, self.sleep_time))
            
            # 获取详情页
            detail_response = session.get(detail_url, headers=self.get_request_headers(), verify=False, timeout=30)
            detail_response.raise_for_status()
            detail_soup = BeautifulSoup(detail_response.text, 'html.parser')
            
            # 提取价格和百分比
            detail_right = detail_soup.select_one('.detail-top-right')
            if not detail_right:
                self.logger.info(f"No detail data found for {cleaned_number}")
                return
                
            # 从最后一个h3元素获取市场价格
            price_element = detail_right.select('h3')[-1] if detail_right.select('h3') else None
            # 从倒数第二个span获取变化百分比
            span_elements = detail_right.select('span')
            percent_element = span_elements[-2] if len(span_elements) >= 2 else None
            
            if price_element and percent_element:
                used_market_price = price_element.text
                change_percent = percent_element.text
                
                # self.logger.info(f"Found price: {used_market_price}, change: {change_percent}")
                
                # 处理获取到的数据
                if self.dump_mode:
                    # 在dump模式下，将数据保存到CSV
                    self.save_to_csv(cleaned_number, used_market_price, change_percent, detail_url)
                else:
                    # 在常规模式下，将报价保存到数据库
                    self.insert_quote(watch_id, used_market_price, change_percent)
                    
                    # 更新手表状态
                    self.update_watch_status(watch_id)
                
                self.logger.info(f"完成: {cleaned_number}")
                self.logger.info('')
            else:
                self.logger.info(f"No pricing data found for {cleaned_number}")
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request error processing {cleaned_number}: {e}")
        except Exception as e:
            self.logger.error(f"Unexpected error processing {cleaned_number}: {e}")
            self.logger.error(traceback.format_exc())

    def handle(self):
        """主执行方法"""
        # 统计需要处理的记录总数
        total_count = self.get_watches_count()
        
        if total_count == 0:
            if self.dump_mode:
                self.logger.info("样本列表为空，无法继续。")
            else:
                self.logger.info("No watches pending for quote fetch.")
            return
        
        self.logger.info(f"Found {total_count} watches to process")
        processed_count = 0
        error_count = 0
        current_page = 1
        
        # 批量处理记录
        while (current_page - 1) * self.limit < total_count:
            self.logger.info(f"Processing batch {current_page}...")
            
            # 获取一批手表
            watches = self.get_watches_batch(current_page)
            
            if not watches:
                self.logger.warning(f"未能获取到第{current_page}批数据，正在尝试下一批...")
                current_page += 1
                if (current_page - 1) * self.limit >= total_count:
                    break
                continue
            
            self.logger.info(f"Processing {len(watches)} watches in this batch")
            
            for watch in watches:
                try:
                    self.process_watch(watch)
                    processed_count += 1
                    
                    # 每处理5个表显示一下进度
                    if processed_count % 5 == 0:
                        progress_percent = (processed_count / total_count) * 100
                        self.logger.info(f"进度: {processed_count}/{total_count} ({progress_percent:.1f}%)")
                        
                except Exception as e:
                    error_count += 1
                    self.logger.error(f"Error processing watch ID {watch.get('id', 'unknown')}: {e}")
                    self.logger.error(traceback.format_exc())
                    
                    # 如果连续错误过多，可能是网络问题，暂停一下
                    if error_count > 5:
                        self.logger.warning("连续多次错误，暂停30秒...")
                        time.sleep(30)
                        error_count = 0
            
            # 移动到下一页
            current_page += 1
        
        if self.dump_mode:
            self.logger.info(f"转储模式处理完成。已处理 {processed_count} 个手表，数据保存在 {self.csv_filename}")
        else:
            self.logger.info(f"Batch processing completed. Processed {processed_count} out of {total_count} watches.")


if __name__ == "__main__":
    try:
        # 检查必要的依赖是否已安装
        missing_packages = []
        try:
            import mysql.connector
        except ImportError:
            missing_packages.append("mysql-connector-python")
        
        try:
            import bs4
        except ImportError:
            missing_packages.append("beautifulsoup4")
            
        # 如果有缺失的包，提示安装并退出
        if missing_packages:
            print("缺少必要的Python包，请安装以下依赖:")
            for package in missing_packages:
                print(f"  pip install {package}")
            print("\n安装完成后请重新运行脚本")
            sys.exit(1)
            
        # 为Windows设置控制台输出编码
        if platform.system() == 'Windows':
            # 设置控制台代码页为UTF-8
            os.system('chcp 65001 >nul')
            # 设置stdout和stderr为UTF-8
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')
        
        parser = argparse.ArgumentParser(description='从timez.cn获取手表价格并更新状态')
        parser.add_argument('--limit', type=int, default=50, help='每批处理的记录数')
        parser.add_argument('--sleep', type=int, default=2, help='请求之间的休眠时间')
        parser.add_argument('--no-proxy', action='store_true', help='不使用代理')
        parser.add_argument('--local-db', action='store_true', help='使用本地数据库(仅Windows)')
        parser.add_argument('--db-host', type=str, help='数据库主机地址')
        parser.add_argument('--db-user', type=str, help='数据库用户名')
        parser.add_argument('--db-pass', type=str, help='数据库密码')
        parser.add_argument('--db-name', type=str, help='数据库名称')
        parser.add_argument('--debug', action='store_true', help='启用调试模式，在错误时暂停')
        parser.add_argument('--dump-mode', action='store_true', help='仅获取数据，不连接数据库')
        
        args = parser.parse_args()
        
        # 如果在Windows上运行并指定了使用本地数据库
        if platform.system() == 'Windows' and (args.local_db or args.db_host):
            # 设置数据库环境变量
            if args.db_host:
                os.environ['DB_HOST'] = args.db_host
            else:
                os.environ['DB_HOST'] = 'localhost'  # 默认本地主机
                
            if args.db_user:
                os.environ['DB_USERNAME'] = args.db_user
            
            if args.db_pass:
                os.environ['DB_PASSWORD'] = args.db_pass
                
            if args.db_name:
                os.environ['DB_DATABASE'] = args.db_name
                
            print(f"在Windows上使用自定义数据库: {os.environ.get('DB_HOST')}")
            
        # 如果使用dump模式，设置特殊环境变量
        if args.dump_mode:
            print("使用数据转储模式，不连接数据库")
            os.environ['DUMP_MODE'] = 'true'
        
        print("Starting fetch_quote script...")
        try:
            fetcher = FetchQuote(limit=args.limit, sleep=args.sleep, use_proxy=not args.no_proxy)
            fetcher.handle() 
            print("Script completed successfully")
        except Exception as e:
            print(f"\n发生严重错误: {e}")
            print("\n详细错误信息:")
            traceback.print_exc()
            
            # 如果启用了调试模式，则在错误时暂停
            if args.debug:
                input("\n按Enter键继续...")
    except Exception as e:
        print(f"Critical error: {e}")
        traceback.print_exc()
        # 确保异常信息写入日志文件
        logging.error(f"Critical error: {e}")
        logging.error(traceback.format_exc())
        
        # 如果启用了调试模式，则在错误时暂停
        if 'args' in locals() and hasattr(args, 'debug') and args.debug:
            input("\n按Enter键继续...") 