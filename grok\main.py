from openai import OpenAI
    
client = OpenAI(
    api_key="************************************************************************************",
    base_url="https://api-proxy.me/xai/v1",
)

# 发送请求
response = client.chat.completions.create(
    model="grok-3",  # 使用 grok-beta 模型
    messages=[
        {"role": "user", "content": "“128238-0132”，帮我提取这句话中的中文品牌名称(brand)和型号(number)，直接返回类似这种json格式{'brand':'','number':''}的字符串"}
    ]
)

# 打印回复
print(response.choices[0].message.content)