#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿里云语音识别配置文件
请根据您的阿里云账号配置以下信息
"""

# 阿里云 AccessKey 配置
ACCESS_KEY_ID = "LTAI5t8WXBXZZfyCaTPGebKy"  # 替换为您的 AccessKey ID
ACCESS_KEY_SECRET = "******************************"  # 替换为您的 AccessKey Secret

# 阿里云语音识别服务配置
APP_KEY = "UwNluehzdlKjETOu"  # 替换为您的 AppKey
REGION_ID = "cn-shanghai"  # 地域ID，通常为 cn-shanghai
ENDPOINT = "nls-meta.cn-shanghai.aliyuncs.com"  # 服务接入点

# 语音识别参数配置
DEFAULT_SAMPLE_RATE = 16000  # 默认采样率
MAX_CHUNK_SIZE = 10 * 1024 * 1024  # 最大分片大小 (10MB)

# 输出配置
DEFAULT_OUTPUT_DIR = "./results"  # 默认输出目录 