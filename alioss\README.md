# 阿里云OSS图片上传工具

这个工具用于将数据库中记录的本地图片上传到阿里云OSS，并更新数据库中的URL地址。

## 功能特点

- 支持断点续传，意外中断后可以从上次的位置继续
- 多线程并行上传，提高处理效率
- 自动重试失败的上传
- 详细的日志记录

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

在 `config.ini` 文件中配置以下信息：

1. 阿里云OSS配置
   - access_key_id: 阿里云访问密钥ID
   - access_key_secret: 阿里云访问密钥Secret
   - endpoint: OSS区域节点
   - bucket_name: OSS存储桶名称
   - oss_prefix: 上传文件的前缀路径

2. 数据库配置
   - host: 数据库主机地址
   - port: 数据库端口
   - user: 数据库用户名
   - password: 数据库密码
   - database: 数据库名称
   - table_name: 表名

3. 任务配置
   - batch_size: 每批处理的图片数量
   - max_workers: 并行工作线程数
   - checkpoint_file: 断点续传文件名
   - retry_attempts: 上传失败重试次数

## 使用方法

```bash
python upload_images.py
```

## 注意事项

- 确保数据库表中的`local_url`字段包含有效的本地图片路径
- 脚本会将上传后的OSS URL更新到同一个`local_url`字段
- 处理大量数据时，建议适当调整batch_size和max_workers参数 