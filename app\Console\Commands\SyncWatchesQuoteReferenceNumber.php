<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncWatchesQuoteReferenceNumber extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-watches-quote-reference {--limit=100 : Number of records to process per batch}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步watches_quote表的reference_number字段，从watches表获取对应的型号';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $limit = $this->option('limit');
            
            // 获取watches_quote表中需要更新的记录数量
            $totalCount = DB::table('watches_quote')
                ->whereNull('reference_number')
                ->count();
                
            if ($totalCount === 0) {
                $this->info('没有需要同步的报价数据');
                return 0;
            }
            
            $this->info("发现 {$totalCount} 条报价记录需要同步型号");
            
            $processedCount = 0;
            $updatedCount = 0;
            
            while (true) {
                // 获取一批需要更新的记录
                $quotes = DB::table('watches_quote')
                    ->whereNull('reference_number')
                    ->orderBy('id')
                    ->limit($limit)
                    ->get();

                if ($quotes->isEmpty()) {
                    $this->info('数据同步完成');
                    break;
                }

                foreach ($quotes as $quote) {
                    try {
                        $processedCount++;
                        
                        // 获取对应的手表记录
                        $watch = DB::table('watches')
                            ->where('id', $quote->watches_id)
                            ->first();
                        
                        if (!$watch) {
                            $this->line("找不到对应的手表记录，watches_id: {$quote->watches_id}，跳过");
                            continue;
                        }
                        
                        // 更新watches_quote表的reference_number字段
                        DB::table('watches_quote')
                            ->where('id', $quote->id)
                            ->update([
                                'reference_number' => $watch->reference_number
                            ]);
                            
                        $this->info("已更新报价记录型号, ID: {$quote->id}, watches_id: {$quote->watches_id}, 型号: {$watch->reference_number}");
                        $updatedCount++;
                        
                    } catch (\Exception $e) {
                        $this->error("更新报价记录型号时出错 ID: {$quote->id}: " . $e->getMessage());
                    }
                }
                
                // 释放内存
                unset($quotes, $watch);
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }
            
            $this->info("型号同步完成. 已处理 {$processedCount} 条记录，实际更新 {$updatedCount} 条.");
            return 0;

        } catch (\Throwable $e) {
            $this->error("程序异常: " . $e->getMessage());
            return 1;
        }
    }
} 