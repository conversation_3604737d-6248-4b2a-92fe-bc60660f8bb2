<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use HeadlessChromium\BrowserFactory;
use HeadlessChromium\Clip;
use HeadlessChromium\Page;
use Illuminate\Support\Carbon;

class TakeWebsiteScreenshot extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:take-screenshot
                            {url : The URL of the website to screenshot}
                            {--x=0 : X coordinate of the clipping rectangle}
                            {--y=0 : Y coordinate of the clipping rectangle}
                            {--width=1920 : Width of the clipping rectangle}
                            {--height=1080 : Height of the clipping rectangle}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '网页截图并保存';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 劳力士[x=660,y=240,width=600,height=600]
        // 欧米茄[x=100,y=100,width=100,height=100]
        // 卡西欧[x=100,y=100,width=100,height=100]
        // 天梭[x=100,y=100,width=100,height=100]
        // 浪琴[x=100,y=100,width=100,height=100]
        // 万国[x=100,y=100,width=100,height=100]
        

        $url = $this->argument('url');
        $x = (int) $this->option('x');
        $y = (int) $this->option('y');
        $width = (int) $this->option('width');
        $height = (int) $this->option('height');

        $this->takeScreenshot($url, $x, $y, $width, $height);
    }

    /**
     * Take a full page screenshot
     */
    private function takeScreenshot(string $url, int $x, int $y, int $width, int $height)
    {
        // 创建浏览器实例
        $browserFactory = new BrowserFactory();
        $browser = $browserFactory->createBrowser([
            'windowSize' => [1920, 1080], // 设置浏览器窗口大小
            'connectionDelay' => 0.8,
            'headless' => true, // 启用无头模式
        ]);

        try {
            // 创建新页面并导航到目标网页
            $page = $browser->createPage();
            $page->navigate($url)->waitForNavigation();

            $clip = new Clip($x, $y, $width, $height);

            // 截取指定区域并保存
            $screenshot = $page->screenshot([
                'clip' => $clip,
                'format' => 'png',
            ]);

            // 存储到storage
            $screenshot->saveToFile('storage/app/public/screenshots/screenshot.png');

            echo "截图已保存为 screenshot.png\n";
        } finally {
            // 关闭浏览器
            $browser->close();
        }
    }
} 