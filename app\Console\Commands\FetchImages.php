<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use QL\QueryList;
use GuzzleHttp\Client;
use HeadlessChromium\BrowserFactory;

class FetchImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fetch-images {min_id} {max_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '时研家图片采集';

    /**
     * 时研家官网
     */
    protected $baseUrl = 'https://www.timez.cn';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $min_id = $this->argument('min_id', 1);
        $max_id = $this->argument('max_id', 100000);

        // 批量获取需要采集的数据
        $browserFactory = new BrowserFactory();
        $browser = $browserFactory->createBrowser([
            'headless' => true,
            'connectionDelay' => 0.8,
            'windowSize' => [1920, 1080],
        ]);

        try {
            $this->recursiveScrapeWebsite($min_id, $max_id, $browser);
        } finally {
            $browser->close();
        }
    }

    /**
     * 递归抓取网页内容
     */
    public function recursiveScrapeWebsite($min_id, $max_id, $browser) 
    {
        $this->info('开始抓取时研家图片...');

        while (true) {

            $watch = DB::table('watches')
                ->where('status', 0)
                ->where('id', '>=', $min_id)
                ->where('id', '<', $max_id)
                ->first();

            if (empty($watch)) {
                $this->info('所有图片采集完成');
                break;
            }

            $url = "https://www.timez.cn/search/{$watch->reference_number}";

            $htmlContent = $this->scrapeWebsite($browser, $url);
            $href = QueryList::html($htmlContent)->find('.watch-list-wrap a:first')->attr('href');

            if (empty($href)) {
                DB::table('watches')
                    ->where('id', $watch->id)
                    ->update([
                        'status' => 2
                    ]);

                $this->info("时研家没有该产品，型号: {$watch->reference_number}");
            }
            else
            {
                $link = "{$this->baseUrl}{$href}"; 
                $htmlContent = $this->scrapeWebsite($browser, $link);

                $images = QueryList::html($htmlContent)
                    ->find('.detail-top-sm-img')
                    ->map(function ($img) {
                        return $img->attr('src');
                    })->all();
                
                if (empty($images)) 
                {
                    $this->info("该产品没有图片: 型号: {$watch->reference_number}");
                    DB::table('watches')
                        ->where('id', $watch->id)
                        ->update([
                            'status' => 2
                        ]);
                } 
                else 
                {
                    // Create directory for images if it doesn't exist
                    $directory = "watches";
                    if (!Storage::exists($directory)) {
                        Storage::makeDirectory($directory);
                    }
                    
                    // Download images
                    foreach ($images as $index => $imageUrl) {
                        // Add a small delay between image downloads
                        if ($index > 0) {
                            usleep(rand(500000, 1000000)); // 0.5-1 second
                        }

                        // 提取后缀名
                        //https://web.cdn.timez.com/rolex/9583f60b80c8dc8a202033c750f3f1d9.png
                        // $ext = pathinfo($imageUrl, PATHINFO_EXTENSION);

                        // 存储到本地
                        try {
                            $filename = basename($imageUrl);
                            $imageContent = file_get_contents($imageUrl);
                            $filePath = "{$directory}/{$filename}";
                            Storage::put($filePath, $imageContent);

                            // 把图片插入到数据库
                            DB::table('waches_new_images')->insert([
                                'watches_id' => $watch->id,
                                'local_url' => $filePath,
                                'original_url' => $imageUrl,
                                'create_time' => Carbon::now(),
                                'update_time' => Carbon::now()
                            ]);

                        } 
                        catch (\Throwable $e) 
                        {
                            $this->info("此图片无法保存: {$imageUrl}");
                            continue;
                        }

                        $this->info("图片已保存: {$imageUrl}");
                    }

                    DB::table('watches')
                        ->where('id', $watch->id)
                        ->update([
                            'status' => 1
                        ]);
                }
            }
        }
    }

    /**
     * 使用无头浏览器抓取网页内容
     */
    private function scrapeWebsite($browser, $url): string
    {
        try {

            $page = $browser->createPage();
            $page->navigate($url)->waitForNavigation();
            sleep(3); // 3秒等待渲染
            $html = $page->evaluate('document.documentElement.outerHTML')->getReturnValue();

            return $html;
        }
        catch (\Throwable $e) 
        {
            $this->error("页面加载失败：" . $e->getMessage());
            return '';
        } 
        finally 
        {
            if (isset($page)) {
                $page->close();
            }
        }
    }
}
