<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class ExportWatchesWithImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:export-watches-with-images 
                            {min_id=1 : 起始ID} 
                            {max_id=999999999 : 结束ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '导出手表数据并下载图片';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $min_id = $this->argument('min_id');
        $max_id = $this->argument('max_id');
        
        $this->info("开始处理手表数据，ID范围: {$min_id} - {$max_id}");
        
        // 创建CSV文件并写入表头
        $csvFile = "D:\\phpEnv\\www\\手表图片和型号\\watches-export-{$min_id}-{$max_id}.csv";
        // if (file_exists($csvFile)) 
        // {
        //     $this->info("CSV文件已存在");
        // } 
        // else 
        // {
        //     $file = fopen($csvFile, 'w');
        //     fputcsv($file, ['品牌', '系列', '型号', '图片1', '图片2', '图片3', '图片4', '图片5', '图片6', '图片7', '图片8', '图片9', '图片10', '图片11', '图片12', '图片13', '图片14', '图片15', '图片16', '图片17', '图片18', '图片19', '图片20']);
        //     fclose($file);
        // }

        // 获取总记录数
        $totalWatches = DB::table('watches')
            ->where('is_image_trained', 0)
            ->where('id', '>=', $min_id)
            ->where('id', '<', $max_id)
            ->count();
            
        $this->info("共找到 {$totalWatches} 条手表记录");
        
        // 循环处理
        while (true) {

            $watch = DB::table('watches')
                ->where('is_image_trained', 0)
                ->where('id', '>=', $min_id)
                ->where('id', '<', $max_id)
                ->first();
            
            if (!$watch) {
                $this->info("ID范围 {$min_id} - {$max_id} 内的所有记录处理完成");
                break;
            }

            $this->processWatch($watch, $csvFile);
            
            // 标记为已处理
            DB::table('watches')
                ->where('id', $watch->id)
                ->update(['is_image_trained' => 1]);
            
            // 释放内存
            gc_collect_cycles();
        }
    }
    
    /**
     * 处理单个手表记录
     */
    private function processWatch($watch, $csvFile)
    {
        try {
            // 获取关联的图片
            $newImages = DB::table('waches_new_images')
                ->where('watches_id', $watch->id)
                ->pluck('original_url')
                ->toArray();
            
            // 合并图片URL并去重
            if (!empty($watch->image_main)) {
                $newImages[] = $watch->image_main;
            }

            $imageUrls = array_unique($newImages);
            
            if (empty($imageUrls)) {
                $this->warn("手表ID {$watch->id} 没有图片，跳过");
                return;
            }
            
            // 根据手表ID创建目录
            $dirName = $watch->id;
            $dirPath = "D:\\phpEnv\\www\\手表图片和型号\\images\\{$dirName}";
            if (!file_exists($dirPath)) {
                mkdir($dirPath, 0777, true);
            }
            
            // 下载图片并保存路径
            $savedPaths = [];
            foreach ($imageUrls as $index => $imageUrl) {
                try {
                    // 添加延迟避免请求过快
                    if ($index > 0) {
                        usleep(rand(500000, 1000000)); // 0.5-1秒
                    }

                    // 生成文件名
                    $filename = basename($imageUrl);
                    
                    // 下载图片
                    $imageContent = @file_get_contents($imageUrl);
                    if ($imageContent === false) {
                        $this->warn("无法下载图片: {$imageUrl}");
                        continue;
                    }
                    
                    // 保存图片
                    file_put_contents("{$dirPath}\\{$filename}", $imageContent);
                    $savedPaths[] = "/{$dirName}/{$filename}";

                } 
                catch (\Exception $e) 
                {
                    $this->info("处理图片时出错: {$e->getMessage()}");
                }
            }
            
            $csvRow = [
                $watch->brand,
                $watch->series,
                $watch->reference_number
            ];
            
            // 添加图片路径 (最多13张)
            for ($i = 0; $i < count($savedPaths); $i++) {
                $csvRow[] = isset($savedPaths[$i]) ? $savedPaths[$i] : '';
            }
            
            // 追加写入CSV
            $file = fopen($csvFile, 'a');
            fputcsv($file, $csvRow);
            fclose($file);

            $this->info("ID：{$watch->id}, 型号：{$watch->reference_number} 处理完成");

        } catch (\Exception $e) {
            $this->info("处理手表ID {$watch->id} 时出错: {$e->getMessage()}");
        }
    }
}
