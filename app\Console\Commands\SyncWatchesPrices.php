<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class SyncWatchesPrices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-watches-prices {--limit=100 : Number of records to process per batch}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步watches表官方指导价，从watches_price表CN价格更新到watches表';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $limit = $this->option('limit');
        
        // 获取watches表中需要更新的手表数量
        $totalCount = DB::table('watches')->count();
            
        if ($totalCount === 0) {
            $this->info('没有需要同步的手表数据');
            return 0;
        }
        
        $this->info("发现 {$totalCount} 款手表需要检查价格数据");
        
        $processedCount = 0;
        $updatedCount = 0;
        
        // 获取所有watches表的ID，按ID升序排列
        $watchIds = DB::table('watches')
            ->orderBy('id')
            ->pluck('id');
            
        $batchSize = $limit;
        $totalBatches = ceil(count($watchIds) / $batchSize);
        
        for ($batch = 0; $batch < $totalBatches; $batch++) {
            $this->info("正在处理第 " . ($batch + 1) . " 批，共 {$totalBatches} 批...");
            
            // 获取当前批次的手表ID
            $batchWatchIds = $watchIds->slice($batch * $batchSize, $batchSize);
            
            foreach ($batchWatchIds as $watchId) {
                try {
                    $processedCount++;
                    
                    // 获取watch记录
                    $watch = DB::table('watches')
                        ->where('id', $watchId)
                        ->first();
                    
                    if (!$watch) {
                        $this->warn("找不到ID为 {$watchId} 的手表记录");
                        continue;
                    }
                    
                    // 获取最新的价格记录
                    $latestPrice = DB::table('watches_price')
                        ->where('watches_id', $watchId)
                        ->where('price_type', 'cn')
                        ->orderBy('id', 'desc')
                        ->first();
                    
                    if (!$latestPrice) {
                        $this->line("ID为 {$watchId} 的手表没有价格记录，跳过");

                        // 将手表ID记录到watches_id_non_price表
                        DB::table('watches_id_non_price')
                            ->insert([
                                'watches_id' => $watchId,
                                'reference_number' => $watch->reference_number,
                            ]);

                        continue;
                    }
                    
                    // 如果价格不同，则更新watches表的官方指导价信息
                    if ($watch->official_quote != $latestPrice->price) {
                        DB::table('watches')
                            ->where('id', $watchId)
                            ->update([
                                'official_quote' => $latestPrice->price
                            ]);
                            
                        $this->info("已更新手表官方指导价, ID: {$watch->id}, 型号: {$watch->reference_number}, 价格: {$latestPrice->price}");
                        $updatedCount++;
                    }
                    
                } catch (\Exception $e) {
                    $this->error("更新手表官方指导价时出错 ID: {$watchId}: " . $e->getMessage());
                }
            }
            
            // 垃圾回收
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
        }
        
        $this->info("官方指导价同步完成. 已处理 {$processedCount} 款手表，实际更新 {$updatedCount} 款.");
        return 0;
    }
} 